<template>
  <div class="medicine-config">
    <!-- 搜索和操作按钮同行布局 -->
    <div class="search-actions-container">
      <div class="search-section">
        <OgwSearch
          :fields="searchFields"
          v-model="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        ></OgwSearch>
      </div>
      <div class="actions-section">
        <el-button type="primary" size="mini" @click="addInfo">新增</el-button>
        <el-button type="primary" size="mini" @click="saveInfo">保存</el-button>
      </div>
    </div>

    <div class="table-container">
      <OgwTable
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-actions="false"
        @cell-change="handleChange"
        @cell-click="handleCellClick"
      >
        <template #instruction="{ row, $index }">
          <el-button
            v-if="!row.id"
            type="text"
            size="mini"
            @click="deleteRow(row, $index)"
          >
            删除</el-button
          >
          <el-button
            v-if="row.id"
            type="text"
            size="mini"
            @click="uploadRow(row)"
          >
            上传</el-button
          >
          <el-button
            v-if="row.id"
            type="text"
            size="mini"
            @click="checkInfo(row)"
            >查看({{
              row.fileInfo ? row.fileInfo.split(",").length : 0
            }})</el-button
          ></template
        >
      </OgwTable>
    </div>
    <FileUpload
      :visible.sync="showUpload"
      :upload-url="`${prefix}/chemicalConfig/upload`"
      :file-types="['.pdf', '.png', '.jpg', 'doc', 'docx']"
      :maxSizeMB="10"
      :fileData="fileData"
      @upload-success="uploadSuccess"
    />
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  getTableList,
  updateMedicineConfig,
  getMedicineInfo,
} from "@/api/ogwWatch/medicineConfig.js";
import { getProd } from "@/api/common.js";
export default {
  name: "medicineConfig",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    // 按顺序加载数据：先加载药剂名称列表，再加载其他数据
    this.initializeData();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
      ];
    },
  },
  data() {
    return {
      deviceOptions: [],
      chemicalNameOptions: [],
      chemicalDataMap: {}, // 存储完整的药剂数据映射，用于联动
      loading: false,
      showUpload: false,
      hzNo: "",
      orgList: [],
      prefix: process.env.VUE_APP_PREFIX,
      searchForm: {
        orgId: "",
        deviceNameCode: "",
      },
      columns: [
        {
          label: "平台名称",
          prop: "productionName",
          editable: false,
          options: [],
        },
        {
          label: "药剂名称",
          prop: "chemicalName",
          editable: true,
          options: [],
        },
        {
          label: "药剂型号",
          prop: "chemicalType",
          editable: true,
          options: [],
        },
        { label: "库存编码", prop: "materialCode", editable: true },
        { label: "推荐加注浓度(ppm)", prop: "reConcentration", editable: true },
        {
          label: "用途",
          prop: "purpose",
          editable: true,
          options: [
            { label: "原油处理", value: "4304030400" },
            { label: "污水处理", value: "4304030100" },
            { label: "注水处理", value: "4304030200" },
            { label: "天然气处理", value: "4304030300" },
            { label: "其他化学药品", value: "4304030500" },
          ],
        },
        {
          label: "费用关注度",
          prop: "attention",
          editable: true,
          options: [
            { label: "重点关注", value: 1 },
            { label: "一般关注", value: 2 },
          ],
        },
        { label: "加注浓度(ppm)", prop: "concentration", editable: true },
        {
          label: "密度(g/m³)",
          prop: "density",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入数字",
          },
        },
        { label: "药剂说明书", prop: "instruction" },
        {
          label: "状态",
          prop: "status",
          editable: true,
          options: [
            {
              label: "启用",
              value: 1,
            },
            {
              label: "停用",
              value: 2,
            },
          ],
        },
      ],
      tableData: [],
      updateData: [], //表格更新的数据
      fileData: {},
    };
  },
  methods: {
    // 初始化数据加载
    async initializeData() {
      try {
        // 先加载药剂名称列表
        await this.getChemicalNameList();
        // 再加载生产平台列表
        await this.getProdList();
        // 最后加载表格数据
        await this.getTableListInfo();
      } catch (error) {
        console.error("数据初始化失败:", error);
      }
    },

    handleSearch(value) {
      this.hzNo = value.deviceNameCode;
      this.getTableListInfo();
    },
    handleReset() {
      // 重置搜索表单
      this.searchForm = {
        orgId: "",
        deviceNameCode: "",
      };
      this.hzNo = "";
      this.getTableListInfo();
    },
    uploadRow(row) {
      this.showUpload = true;
      this.fileData = { id: row.id };
    },
    checkInfo(row) {
      const fileIds = row.fileInfo || null;

      this.$router.push({
        name: "opinionsPre",
        params: { fileIds },
      });
    },
    deleteRow(row, index) {
      this.tableData.splice(index, 1);
      this.updateData = this.updateData.filter((item) => item !== index);
    },
    addInfo() {
      this.columns[0].editable = true;
      // 清空药剂型号选项，新增时需要先选择药剂名称
      this.$set(this.columns[2], "options", []);

      this.tableData.unshift({
        productionName: "",
        chemicalName: "", // 药剂名称
        chemicalType: "", // 药剂型号
        materialCode: "", // 库存编码
        reConcentration: "",
        purpose: "",
        attention: "",
        concentration: "",
        density: "",
        instruction: "",
      });
    },
    async getTableListInfo() {
      this.loading = true;
      try {
        const res = await getTableList(this.hzNo || "");
        if (res.code === 200) {
          this.tableData = res.data;
          // 数据加载完成后，为每行初始化药剂型号选项
          this.initializeChemicalTypeOptions();
        } else {
          this.tableData = [];
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        this.loading = false;
      }
    },

    // 初始化药剂型号选项（用于编辑模式）
    initializeChemicalTypeOptions() {
      console.log("初始化药剂型号选项", {
        tableDataLength: this.tableData.length,
        chemicalNameOptionsLength: this.chemicalNameOptions.length
      });

      // 如果药剂名称选项还没有加载完成，不进行初始化
      if (!this.chemicalNameOptions || this.chemicalNameOptions.length === 0) {
        console.log("药剂名称选项未加载，跳过初始化");
        return;
      }

      // 为每行数据设置初始的药剂型号选项
      // 注意：这里我们不能为所有行设置相同的选项，因为每行的药剂名称可能不同
      // 所以我们只是确保有数据的行在需要时能正确显示选项
      let hasValidData = false;
      this.tableData.forEach((row) => {
        if (row.chemicalName) {
          hasValidData = true;
        }
      });

      // 如果有有效数据，我们先清空选项，让用户点击时动态加载
      if (hasValidData) {
        this.$set(this.columns[2], "options", []);
        console.log("已清空药剂型号选项，等待用户点击时动态加载");
      }
    },
    async saveInfo() {
      // set集合转成数组
      const targetArr = Array.from(new Set(this.updateData));

      const targetObj = targetArr.map((item) => this.tableData[item]);

      const res = await updateMedicineConfig(targetObj);
      if (res.code === 200) {
        this.$message.success("保存成功");
        this.getTableListInfo();
      }
    },
    handleCellClick({ row, prop, index }) {
      console.log("Cell clicked:", { row, prop, index });

      // 如果点击的是药剂型号列，需要根据药剂名称设置选项
      if (prop === "chemicalType") {
        this.handleChemicalTypeClick(row);
      }
    },

    // 处理药剂型号列点击事件
    handleChemicalTypeClick(row) {
      // 检查当前行是否有药剂名称
      if (!row.chemicalName) {
        // 如果没有药剂名称，提示用户先选择药剂名称
        this.$message.warning("请先选择药剂名称");
        // 清空药剂型号选项，防止用户选择
        this.$set(this.columns[2], "options", []);
        return;
      }

      // 根据当前行的药剂名称更新药剂型号选项
      this.updateChemicalTypeOptions(row);
    },

    // 获取指定药剂名称对应的药剂型号选项
    getChemicalTypeOptions(chemicalName) {
      if (!chemicalName || !this.chemicalNameOptions) {
        return [];
      }

      const selectedChemical = this.chemicalNameOptions.find((item) => {
        return item.value === chemicalName;
      });

      if (selectedChemical && selectedChemical.child && selectedChemical.child.length > 0) {
        return selectedChemical.child.map((item) => ({
          label: item.chemicalType,
          value: item.chemicalType,
          id: item.id,
        }));
      }

      return [];
    },
    handleChange(row) {
      this.updateData.push(row.index);

      // 实现药剂名称联动功能
      if (row.prop === "chemicalName") {
        this.handleChemicalNameChange(row);
      }

      if (row.prop === "productionName") {
        const hzNo = this.deviceOptions.find(
          (item) => item.label === row.value
        ).hzNo;
        this.$set(this.tableData[row.index], "hzNo", hzNo);
      }
    },

    // 处理药剂名称变化的联动逻辑
    handleChemicalNameChange({ row, value, index }) {
      console.log("药剂名称变化:", { row, value, index });

      // 清空药剂型号
      this.$set(this.tableData[index], "chemicalType", "");

      // 更新药剂型号的选项
      this.updateChemicalTypeOptions(row);
    },

    // 更新药剂型号选项
    updateChemicalTypeOptions(row) {
      console.log("更新药剂型号选项:", {
        chemicalName: row.chemicalName,
        chemicalNameOptionsLength: this.chemicalNameOptions?.length || 0
      });

      // 使用辅助方法获取选项
      const options = this.getChemicalTypeOptions(row.chemicalName);

      console.log("获取到的药剂型号选项:", options);

      // 设置药剂型号列的选项
      this.$set(this.columns[2], "options", options);
    },
    uploadSuccess() {
      this.showUpload = false;
      this.getTableListInfo();
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
        this.deviceOptions = this.orgList
          .map((item) => {
            return item.children.map((c) => {
              return {
                label: c.label,
                value: c.label,
                hzNo: c.value,
              };
            });
          })
          .flat();
        this.searchForm.orgId = this.orgList[0].orgId;
        this.columns[0].options = this.deviceOptions;
      }
    },
    async getChemicalNameList() {
      const res = await getMedicineInfo();
      if (res.code === 200) {
        // 构建药剂选项数据
        this.chemicalNameOptions = res.data.map((item) => {
          return {
            label: item.chemicalName,
            value: item.prdCode,
            child: item.children,
          };
        });

        // 设置药剂名称列的选项
        this.$set(this.columns[1], "options", this.chemicalNameOptions);

        // 如果已有表格数据，初始化药剂型号选项
        if (this.tableData.length > 0) {
          this.initializeChemicalTypeOptions();
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-config {
  padding: 20px;
}

/* 搜索和操作按钮同行布局 */
.search-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;

  .search-section {
    flex: 1;
    min-width: 0; // 防止flex子项溢出

    // 重置OgwSearch组件的默认margin
    ::v-deep .search-bar {
      margin: 0;
    }
  }

  .actions-section {
    flex-shrink: 0;
    display: flex;
    gap: 8px;
    align-items: center;

    .el-button {
      margin-left: 0;
    }
  }
}

/* 表格容器样式已移至search-actions-container的margin-bottom */

/* 响应式设计 */
@media (max-width: 768px) {
  .search-actions-container {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;

    .actions-section {
      justify-content: flex-end;
    }
  }
}

/* 主题样式 */
[data-theme="dark"] .medicine-config {
  background: #162549;
}

[data-theme="tint"] .medicine-config {
  background: #fff;
}
</style>
